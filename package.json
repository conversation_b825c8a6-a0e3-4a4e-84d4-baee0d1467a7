{"name": "arco-design-pro", "version": "1.0.0", "description": "Arco Design Pro", "scripts": {"start": "vite", "dev": "vite", "preview": "vite preview", "build": "vite build", "eslint": "eslint src/ --ext .ts,.tsx,.js,.jsx --fix --cache", "stylelint": "stylelint 'src/**/*.less' 'src/**/*.css' --fix --cache", "pre-commit": "pretty-quick --staged && npm run eslint && npm run stylelint", "prepare": "husky install && husky add .husky/pre-commit 'npm run pre-commit'"}, "dependencies": {"@antv/data-set": "^0.11.8", "@arco-design/color": "^0.4.0", "@arco-design/web-react": "^2.32.2", "@arco-plugins/vite-react": "^1.0.5", "@arco-themes/react-arco-pro": "^0.0.7", "@loadable/component": "^5.13.2", "@turf/turf": "^6.5.0", "arco-design-pro": "^2.8.1", "axios": "^0.24.0", "bizcharts": "^4.1.11", "classnames": "^2.3.1", "copy-to-clipboard": "^3.3.1", "dayjs": "^1.11.13", "lodash": "^4.17.21", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "query-string": "^6.13.8", "react": "^17.0.2", "react-color": "^2.18.1", "react-dom": "^17.0.2", "react-redux": "^7.2.6", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "redux": "^4.1.2"}, "devDependencies": {"@arco-design/webpack-plugin": "^1.6.0", "@arco-plugins/vite-plugin-svgr": "^0.7.2", "@svgr/webpack": "^5.5.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vitejs/plugin-react": "^1.1.0", "eslint": "^8.10.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^7.0.2", "less": "^4.1.2", "less-loader": "7.3.0", "postcss-less": "4", "prettier": "^2.4.1", "pretty-quick": "^3.1.2", "stylelint": "^14.1.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^24.0.0", "typescript": "^4.5.2", "vite": "^2.6.14"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix --cache"], "*.{css, less}": ["stylelint --fix"]}}